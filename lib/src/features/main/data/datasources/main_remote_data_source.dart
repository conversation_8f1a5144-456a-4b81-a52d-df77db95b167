import 'dart:convert' show json;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat_application/src/core/utils/managers/http/http_methods.dart' show HttpMethods;
import 'package:kurdsat_application/src/core/api/api.dart' show Api;
import 'package:kurdsat_application/src/core/utils/managers/http/http_manager.dart' show HttpManager, httpManagerProvider;

class MainRemoteDataSource {
  final HttpManager httpManager;

  MainRemoteDataSource({required this.httpManager});

  Future<Map<String, dynamic>> fetchMain() async {
    final response = await httpManager.request(
      path: Api().baseUrl,
      method: HttpMethods.get,
    );

    return json.decode(response.data as String) as Map<String, dynamic>;
  }
}

final mainDatasourceProvider = Provider<MainRemoteDataSource>((ref) {
  return MainRemoteDataSource(httpManager: ref.read(httpManagerProvider));
});
