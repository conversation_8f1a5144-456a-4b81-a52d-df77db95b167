import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:kurdsat_application/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat_application/src/core/utils/helpers/error_parser.dart' show errorParser;
import 'package:kurdsat_application/src/features/main/data/datasources/main_remote_data_source.dart' show MainRemoteDataSource;
import 'package:kurdsat_application/src/features/main/data/mappers/main_mapper.dart' show MainMapper;
import 'package:kurdsat_application/src/features/main/data/models/main_model_data.dart' show MainModelData;
import 'package:kurdsat_application/src/features/main/domain/entities/main_entity.dart' show MainEntity;
import 'package:kurdsat_application/src/features/main/domain/repositories/main_repository.dart' show MainRepository;

class MainRepositoryImpl implements MainRepository {
  final MainRemoteDataSource mainRemoteDataSource;

  MainRepositoryImpl({required this.mainRemoteDataSource});

  @override
  Future<Either<ErrorModel, List<MainEntity>>> fetchMain() async {
    try {
      final result = await mainRemoteDataSource.fetchMain();

      MainModelData model = MainModelData.fromJson(result);

      MainMapper mapper = MainMapper(mainModelData: model);

      final List<MainEntity> finalResponse = mapper.toMainEntity();

      return Right(finalResponse);
    } catch (error, stackTrace) {
      log(error.toString(), stackTrace: stackTrace);
      return Left(errorParser(error, stackTrace));
    }
  }
}




