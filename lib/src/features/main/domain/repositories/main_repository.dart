import 'package:dartz/dartz.dart' show Either;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat_application/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat_application/src/features/main/data/datasources/main_remote_data_source.dart' show mainDatasourceProvider;
import 'package:kurdsat_application/src/features/main/data/repositories/main_reposetory_impl.dart' show MainRepositoryImpl;
import 'package:kurdsat_application/src/features/main/domain/entities/main_entity.dart' show MainEntity;

abstract class MainRepository {
  Future<Either<ErrorModel, List<MainEntity>>> fetchMain();
}

final mainRepositoryProvider = Provider<MainRepository>((ref) {
  return MainRepositoryImpl(mainRemoteDataSource: ref.read(mainDatasourceProvider));
});
