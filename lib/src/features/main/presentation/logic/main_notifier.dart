import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import "package:kurdsat_application/src/features/main/domain/entities/main_entity.dart" show MainEntity;
import "package:kurdsat_application/src/features/main/domain/repositories/main_repository.dart";


class MainNotifier extends AsyncNotifier<List<MainEntity>> {
  late MainRepository _mainRepositoryProvider;

  @override
  FutureOr<List<MainEntity>> build() async {
    _mainRepositoryProvider = ref.read(mainRepositoryProvider);
    await fetchProducts();
    return state.value ?? [];
  }

  Future<void> fetchProducts() async {
    state = const AsyncValue.loading();

    final response = await _mainRepositoryProvider.fetchMain();

    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right);
      },
    );
  }
}

final mainNotifierProvider = AsyncNotifierProvider<MainNotifier, List<MainEntity>>(MainNotifier.new);
