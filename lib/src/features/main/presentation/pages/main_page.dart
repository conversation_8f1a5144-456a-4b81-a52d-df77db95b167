import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:kurdsat_application/src/features/main/presentation/logic/main_notifier.dart' show mainNotifierProvider;



class MainPage extends ConsumerStatefulWidget {
  const MainPage({super.key});

  @override
  ConsumerState<MainPage> createState() => _MainPageState();
}

class _MainPageState extends ConsumerState<MainPage> {
  @override
  Widget build(BuildContext context) {
    final mainState = ref.watch(mainNotifierProvider);
    return Scaffold(
      backgroundColor: Color(0xff0A0C10),
      body: SingleChildScrollView(
        child: Column(
          children: [
            24.verticalSpace,
            // Continue

            mainState.maybeWhen(
              orElse: () => Text("Loading..."),
              data: (data) => Text("Data loaded: ${data.length} items"),
              loading: () => CircularProgressIndicator(),
              error: (error, stack) => Text("Error: $error"),
            ),
          ],
        ),
      ),
    );
  }
}
