import '../../domain/repositories/check_first_launch_repository.dart';
import '../datasources/local/first_time_launched_data_source.dart';

class CheckFirstLaunchRepositoryImpl implements CheckFirstLaunchRepository {
  final FirstTimeAppLaunchedDataSource firstTimeAppLaunchedDataSource;

  CheckFirstLaunchRepositoryImpl({
    required this.firstTimeAppLaunchedDataSource,
  });

  @override
  Future<bool> checkFirstLaunch() async {
    final bool isFirstTimeLaunched =
        await firstTimeAppLaunchedDataSource.getIsFirstAppLaunch();

    if (isFirstTimeLaunched) {
      firstTimeAppLaunchedDataSource.markAsLaunched();
    }
    return isFirstTimeLaunched;
  }

  @override
  Future<void> markAsLaunched() async {
    firstTimeAppLaunchedDataSource.markAsLaunched();
  }
}
