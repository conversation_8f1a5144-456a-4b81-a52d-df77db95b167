import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import 'app_status_model_data.dart';

part 'app_status_model.g.dart';

@JsonSerializable()
class AppStatusModel extends Equatable {
  final String? message;
  final AppStatusModelData? data;

  const AppStatusModel({this.message, this.data});

  factory AppStatusModel.fromJson(Map<String, dynamic> json) {
    return _$AppStatusModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AppStatusModelToJson(this);

  AppStatusModel copyWith({
    String? message,
    AppStatusModelData? data,
  }) {
    return AppStatusModel(
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  @override
  List<Object?> get props => [message, data];
}
