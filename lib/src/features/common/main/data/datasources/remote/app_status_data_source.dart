import 'dart:convert';

import 'package:dio/dio.dart';

import '../../../../../../core/common/domain/entities/custom_header.dart';
import '../../../../../../core/utils/managers/http/http_manager.dart';
import '../../../../../../core/utils/managers/http/http_methods.dart';

abstract class AppStatusDataSource {
  Future<Map<String, dynamic>> getData(CustomHeaders header);
}

class AppStatusDataSourceImpl implements AppStatusDataSource {
  final HttpManager httpManager;

  AppStatusDataSourceImpl({required this.httpManager});

  @override
  Future<Map<String, dynamic>> getData(CustomHeaders header) async {
    final Response response = await httpManager.request(method: HttpMethods.get, path: "/appstatus", headers: header.value);
    return json.decode(response.data as String) as Map<String, dynamic>;
  }
}
