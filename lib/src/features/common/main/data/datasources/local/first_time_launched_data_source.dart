import '../../../../../../core/utils/managers/database/database_manager.dart';

abstract class FirstTimeAppLaunchedDataSource {
  Future<bool> getIsFirstAppLaunch();
  Future<void> markAsLaunched();
}

class FirstTimeAppLaunchedDataSourceImpl implements FirstTimeAppLaunchedDataSource {
  DatabaseManager databaseManager;

  FirstTimeAppLaunchedDataSourceImpl({required this.databaseManager});

  @override
  Future<bool> getIsFirstAppLaunch() async {
    return await databaseManager.getData("isFirstLaunch") as bool? ?? true;
  }

  @override
  Future<void> markAsLaunched() async {
    databaseManager.saveData("isFirstLaunch", false);
  }
}
