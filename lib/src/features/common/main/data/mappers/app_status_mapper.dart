import '../../../../../core/constants/const.dart';
import '../../domain/entities/app_status.dart';
import '../models/app_status_model_data.dart';

class AppStatusEntityMapper {
  final AppStatusModelData? appStatusModelData;
  final String? installedVersion;
  final bool? isHms;
  AppStatusEntityMapper({this.appStatusModelData, this.installedVersion, this.isHms});

  AppStatus toAppStatus() {
    return AppStatus(forceUpdate: appStatusModelData?.forceUpdate ?? false, iosUrl: appStatusModelData?.iosUrl ?? "", androidUrl: isHms ?? false ? appStatusModelData?.huaweiUrl ?? kHuaweiURL : appStatusModelData?.androidUrl ?? "", latestVersion: appStatusModelData?.latestVersion ?? "", maintenance: appStatusModelData?.maintenance ?? false, installedAppVersion: installedVersion ?? "");
  }
}
