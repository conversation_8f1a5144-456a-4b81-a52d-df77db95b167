import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

import 'store_download_info_entity.dart';
import 'update_info_entity.dart';

class AppStatus extends Equatable {
  late final bool? isMaintenance;
  late final UpdateInfo updateInfo;
  late final bool? isUpdate;
  late final bool? isWeb;
  late final DownloadAppInfo downloadAppInfo;

  AppStatus({
    final String? androidUrl,
    final String? iosUrl,
    final String? latestVersion,
    final String? installedAppVersion,
    required final bool forceUpdate,
    final bool? maintenance,
  }) {
    isMaintenance = maintenance;
    isWeb = kIsWeb;

    isUpdate = checkIsUpdateNeeded(
      installedAppVersion ?? "",
      latestVersion ?? "",
    );

    final String updateUrl =
        Platform.isAndroid ? androidUrl ?? "" : iosUrl ?? "";
    final bool isAndroid = Platform.isAndroid;

    updateInfo = UpdateInfo(
      updateUrl: updateUrl,
      forceUpdate: forceUpdate,
      isAndroid: isAndroid,
    );

    downloadAppInfo = DownloadAppInfo(
      androidUrl: androidUrl,
      iosUrl: iosUrl,
    );
  }

  bool checkIsUpdateNeeded(String installedAppVersion, String latestVersion) {
    final double installedAppVersionDouble =
        double.tryParse(installedAppVersion.trim().replaceAll(".", "")) ?? 0.0;
    final double latestVersionDouble =
        double.tryParse(latestVersion.trim().replaceAll(".", "")) ?? 0.0;

    return latestVersionDouble > installedAppVersionDouble;
  }

  @override
  List<Object?> get props {
    return [
      updateInfo,
      isMaintenance,
      isWeb,
    ];
  }
}
