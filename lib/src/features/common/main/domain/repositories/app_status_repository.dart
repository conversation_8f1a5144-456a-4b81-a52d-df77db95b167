import 'package:dartz/dartz.dart';

import '../../../../../core/common/data/models/error_model/error_model.dart';
import '../../../../../core/common/domain/entities/custom_header.dart';
import '../entities/app_status.dart';

/// Repository interface for app status
abstract class AppStatusRepository {
  ///get app status data
  Future<Either<ErrorModel, AppStatus>> getAppStatusData(CustomHeaders header);
}
