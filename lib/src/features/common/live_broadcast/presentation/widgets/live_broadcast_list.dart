import 'package:flutter/material.dart';
import 'package:kurdsat_application/src/features/common/live_broadcast/domain/entities/live_broadcast_entity.dart';
import 'package:kurdsat_application/src/features/common/live_broadcast/presentation/widgets/live_broadcast_tile.dart';

class LiveBroadcastList extends StatelessWidget {
  final List<LiveBroadcastEntity> broadcasts;

  const LiveBroadcastList({
    super.key,
    required this.broadcasts,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: broadcasts.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        return LiveBroadcastTile(broadcast: broadcasts[index]);
      },
    );
  }
}
