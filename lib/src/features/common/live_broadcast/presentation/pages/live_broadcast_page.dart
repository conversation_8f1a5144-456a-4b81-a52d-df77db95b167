import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat_application/src/features/common/live_broadcast/presentation/widgets/live_broadcast_list.dart';
import 'package:kurdsat_application/src/core/enums/channel_type_enum.dart';
import 'package:kurdsat_application/src/features/common/live_broadcast/domain/entities/live_broadcast_entity.dart';

class LiveBroadcastPage extends ConsumerStatefulWidget {
  const LiveBroadcastPage({super.key});

  @override
  ConsumerState<LiveBroadcastPage> createState() => _LiveBroadcastPageState();
}

class _LiveBroadcastPageState extends ConsumerState<LiveBroadcastPage> {
  @override
  void initState() {
    super.initState();
    // Initialize live broadcasts when the page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // ref.read(liveBroadcastNotifierProvider.notifier).getLiveBroadcasts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Text("Live Broadcast"),
      ),
    );
    // return Scaffold(
    //   appBar: AppBar(
    //     title: const Text('Live Broadcast'),
    //     backgroundColor: Theme.of(context).primaryColor,
    //     foregroundColor: Colors.white,
    //   ),
    //   body: RefreshIndicator(
    //     onRefresh: () async {
    //       await ref.read(liveBroadcastNotifierProvider.notifier).getLiveBroadcasts();
    //     },
    //     child: _buildBody(isLoading, error, broadcasts),
    //   ),
    // );
  }

  Widget _buildBody(bool isLoading, String? error, List<LiveBroadcastEntity> broadcasts) {
    if (isLoading && broadcasts.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (error != null && broadcasts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load live broadcasts',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // ref.read(liveBroadcastNotifierProvider.notifier).getLiveBroadcasts();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (broadcasts.isEmpty) {
      return const Center(
        child: Text('No live broadcasts available'),
      );
    }

    // Separate TV and Radio channels
    final tvChannels = broadcasts.where((b) => b.channelType == ChannelType.tv).toList();
    final radioChannels = broadcasts.where((b) => b.channelType == ChannelType.radio).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (tvChannels.isNotEmpty) ...[
            Text(
              'TV Channels',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            LiveBroadcastList(broadcasts: tvChannels),
            const SizedBox(height: 24),
          ],
          if (radioChannels.isNotEmpty) ...[
            Text(
              'Radio Channels',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            LiveBroadcastList(broadcasts: radioChannels),
          ],
        ],
      ),
    );
  }
}
