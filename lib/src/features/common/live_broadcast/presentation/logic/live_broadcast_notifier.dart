import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat_application/src/core/enums/channel_type_enum.dart' show ChannelType;
import 'package:kurdsat_application/src/features/common/live_broadcast/domain/entities/live_broadcast_entity.dart';

class LiveBroadcastNotifier extends AsyncNotifier<LiveBroadcastEntity> {
  // final LiveBroadcastRepository _liveBroadcastsRepository;

  @override
  FutureOr<LiveBroadcastEntity> build() async {
    // _liveBroadcastsRepository = ref.read(liveBroadcastsRepositoryProvider);

    await getLiveBroadcasts();

    return state.value ?? const LiveBroadcastEntity(id: 0, title: '', link: '', channelType: ChannelType.tv);
  }

  Future<void> getLiveBroadcasts() async {
    state = const AsyncValue.loading();

    // final result = await _liveBroadcastsRepository.getLiveBroadcasts();

    // result.fold(
    // (error) => state = AsyncValue.error(error.error?.message ?? 'Unknown error occurred', StackTrace.fromString(error.error?.message ?? "")),
    // (List<LiveBroadcastEntity> broadcasts) => state = AsyncValue.data(broadcasts),
    // );
  }
}
