import 'package:kurdsat_application/src/core/common/data/models/general_model.dart';
import 'package:kurdsat_application/src/features/common/live_broadcast/data/models/channel_type_model.dart' show ChannelTypeModel;
import 'package:kurdsat_application/src/features/common/live_broadcast/domain/entities/channel_type_entity.dart' show ChannelTypeEntity;
import 'package:kurdsat_application/src/core/enums/channel_type_enum.dart' show ChannelTypeEnum;

import '../../domain/entities/broadcast_entity.dart' show BroadcastEntity;
import '../models/broadcast_model.dart' show BroadcastModel;

class LiveBroadcastMapper {
  BroadcastEntity toEntity(BroadcastModel model) {
    return BroadcastEntity(
      title: model.title,
      link: model.link,
      channelType: model.channelType?.data?.toEntity(),
      image: model.image?.data?.toEntity(),
    );
  }
}

class ChannelTypeMapper {
  // toEntity() {}
  ChannelTypeEntity toEntity(ChannelTypeModel model) {
    return ChannelTypeEntity(
      id: model.id ?? 0,
      title: ChannelTypeEnum.fromString(model.title),
    );
  }
}



// return entity
// accept model