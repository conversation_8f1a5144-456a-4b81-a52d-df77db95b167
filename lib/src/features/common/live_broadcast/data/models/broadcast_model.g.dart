// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'broadcast_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BroadcastModel _$BroadcastModelFromJson(
  Map<String, dynamic> json,
) => BroadcastModel(
  title: json['title'] as String?,
  link: json['link'] as String?,
  channelType:
      json['channelType'] == null
          ? null
          : StrapiResponse<ChannelTypeModel>.fromJson(
            json['channelType'] as Map<String, dynamic>,
            (value) => ChannelTypeModel.fromJson(value as Map<String, dynamic>),
          ),
  image:
      json['image'] == null
          ? null
          : StrapiResponse<ThumbnailModel>.fromJson(
            json['image'] as Map<String, dynamic>,
            (value) => ThumbnailModel.fromJson(value as Map<String, dynamic>),
          ),
);

Map<String, dynamic> _$BroadcastModelToJson(BroadcastModel instance) =>
    <String, dynamic>{
      'title': instance.title,
      'link': instance.link,
      'channelType': instance.channelType?.toJson((value) => value),
      'image': instance.image?.toJson((value) => value),
    };
