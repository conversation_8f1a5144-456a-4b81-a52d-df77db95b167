import 'package:json_annotation/json_annotation.dart';
import 'package:kurdsat_application/src/core/common/data/models/general_model.dart';

import 'channel_type_model.dart' show ChannelTypeModel;
import 'thumbnail_model.dart' show ThumbnailModel;

part 'broadcast_model.g.dart';

@JsonSerializable()
class BroadcastModel {
  final String? title;
  final String? link;
  final StrapiResponse<ChannelTypeModel>? channelType;
  final StrapiResponse<ThumbnailModel>? image;

  const BroadcastModel({
    this.title,
    this.link,
    this.channelType,
    this.image,
  });

  factory BroadcastModel.fromJson(Map<String, dynamic> json) => _$BroadcastModelFromJson(json);

  Map<String, dynamic> toJson() => _$BroadcastModelToJson(this);
}
