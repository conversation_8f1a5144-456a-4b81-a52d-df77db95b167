import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat_application/src/core/api/api.dart' show Api;
import 'package:kurdsat_application/src/core/utils/managers/http/http_manager.dart';
import 'package:kurdsat_application/src/core/utils/managers/http/http_methods.dart';

class LiveBroadcastRemoteDataSource {
  final HttpManager httpManager;

  LiveBroadcastRemoteDataSource({required this.httpManager});

  Future<Map<String, dynamic>> getLiveBroadcasts() async {
    final response = await httpManager.request(
      path: Api().kurdsatNewsChannels,
      method: HttpMethods.get,
    );
    return json.decode(response.data as String) as Map<String, dynamic>;
  }
}

final liveBroadcastRemoteDataSourceProvider = Provider<LiveBroadcastRemoteDataSource>((ref) {
  return LiveBroadcastRemoteDataSource(httpManager: ref.read(httpManagerProvider));
});
