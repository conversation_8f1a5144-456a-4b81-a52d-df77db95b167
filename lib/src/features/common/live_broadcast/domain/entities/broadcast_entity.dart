import 'package:equatable/equatable.dart';
import 'package:kurdsat_application/src/core/common/domain/entities/strapi_response_entity.dart';
import 'package:kurdsat_application/src/features/common/live_broadcast/domain/entities/channel_type_entity.dart';

import 'thumbnail_entity.dart' show ThumbnailEntity;

class BroadcastEntity extends Equatable {
  final String? title;
  final String? link;
  final StrapiResponseEntity<ChannelTypeEntity>? channelType;
  final StrapiResponseEntity<ThumbnailEntity>? image;

  const BroadcastEntity({
    this.title,
    this.link,
    this.channelType,
    this.image,
  });

  @override
  List<Object?> get props => [title, link, channelType, image];
}
