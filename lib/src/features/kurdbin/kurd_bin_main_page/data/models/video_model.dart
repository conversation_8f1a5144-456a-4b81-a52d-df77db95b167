import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/data/models/thumbnail_model.dart';

import '../../../../../core/utils/helpers/image_url_builder.dart';
import '../../domain/entities/kurdbin_show_card.dart';
import '../enums/image_orientation_enum.dart';
import 'banner_model.dart';

class VideoModel {
  int? id;
  String? title;
  BannerModel? bannerModel;
  ThumbnailModel? thumbnail;

  VideoModel({this.id, this.title, this.bannerModel, this.thumbnail});

  VideoModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    bannerModel = json['banner'] != null ? BannerModel.fromJson(json['banner']) : null;
    thumbnail = json['thumbnail'] != null ? ThumbnailModel.fromJson(json['thumbnail']) : null;
  }

  KurdbinShowCardEntity toKurdbinShowCardEntity() {
    return KurdbinShowCardEntity(title: title ?? "", image: constructKurdbinImageUrl(thumbnail?.url ?? ""));
  }
}
