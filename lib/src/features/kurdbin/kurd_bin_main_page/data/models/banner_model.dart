import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/data/models/thumbnail_model.dart';

class BannerModel {
  int? id;
  ThumbnailModel? landScapeImage;
  ThumbnailModel? portraitImage;

  BannerModel({
    this.id,
    this.landScapeImage,
    this.portraitImage,
  });

  BannerModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    landScapeImage = json['landscape'] != null ? ThumbnailModel.fromJson(json['landscape']) : null;
    portraitImage = json['portrait'] != null ? ThumbnailModel.fromJson(json['portrait']) : null;
  }
}
