import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/data/models/kurdbin_section_model.dart';
import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/domain/entities/kurd_bin_main_page_entity.dart' show KurdBinMainPageEntity;

class KurdBinMainPageMapper {
  final List<KurdBinSectionModel> list;

  KurdBinMainPageMapper({required this.list});

  KurdBinMainPageEntity toKurdBinMainPageEntity() {
    List<String> categories = list.map((e) => e.title ?? "").toList();

    return KurdBinMainPageEntity(categories: categories, kurdBinSections: list.map((e) => e.toKurdBinSectionEntity()).toList());
  }
}
