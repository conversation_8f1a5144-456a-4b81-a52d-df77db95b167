import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/data/enums/image_orientation_enum.dart';
import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/domain/entities/kurd_bin_main_page_entity.dart';
import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/presentation/widgets/horizontal_show_design.dart';
import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/presentation/widgets/vertical_show_design_widget.dart';

import '../../domain/entities/kurdbin_section.dart';

class KurdbinPageContent extends StatelessWidget {
  final KurdBinMainPageEntity data;
  final String cat;

  const KurdbinPageContent({
    super.key,
    required this.data,
    required this.cat,
  });

  @override
  Widget build(BuildContext context) {
    if (cat == "all") {
      return SizedBox();
    } else {
      return ListView.builder(
        scrollDirection: Axis.vertical,
        itemCount: data.kurdBinSections.length,
        itemBuilder: (context, index) {
          final KurdBinSectionEntity sectionEntity = data.kurdBinSections[index];
          return Column(
            children: [
              16.verticalSpace,

              if (sectionEntity.orientationEnum == OrientationEnum.portrait) ...[
                VerticalShowDesignWidget(sectionEntity: sectionEntity),
              ] else ...[
                HorizontalShowDesignWidget(sectionEntity: sectionEntity),
              ],
            ],
          );
        },
      );
    }
  }
}
