import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat_application/src/core/common/widgets/category_title_section.dart' show CategoryTitleSection;
import 'package:kurdsat_application/src/core/common/widgets/news_card/news_card_view.dart';
import '../../domain/entities/kurdbin_section.dart';

class HorizontalShowDesignWidget extends StatefulWidget {
  final KurdBinSectionEntity sectionEntity;

  const HorizontalShowDesignWidget({
    super.key,
    required this.sectionEntity,
  });

  @override
  State<HorizontalShowDesignWidget> createState() => _HorizontalShowDesignWidgetState();
}

class _HorizontalShowDesignWidgetState extends State<HorizontalShowDesignWidget> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final hasEnoughShows = widget.sectionEntity.showsList.length >= 2;
    if (!hasEnoughShows) return const SizedBox.shrink();

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.0.r),
          child: CategoryTitleSection(title: widget.sectionEntity.title),
        ),
        16.verticalSpace,
        CarouselSlider.builder(
          itemCount: widget.sectionEntity.showsList.length,
          itemBuilder: (context, index, realIndex) {
            final article = widget.sectionEntity.showsList[index];
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              // adjust spacing here
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: NewsCardView(
                      imageUrl: article.image,
                      title: article.title,
                    ),
                  ),
                ],
              ),
            );
          },
          options: CarouselOptions(
            height: 300.r,
            viewportFraction: 0.92,
            pageSnapping: true,
            enableInfiniteScroll: false,
            onPageChanged: (index, reason) {
              HapticFeedback.mediumImpact();
              setState(() {
                _currentIndex = index;
              });
            },
          ),
        ),
        16.verticalSpace,

        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            widget.sectionEntity.showsList.length,
            (index) => AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.fastOutSlowIn,

              width: 8.r,
              height: 8.r,
              margin: EdgeInsets.symmetric(horizontal: 4.r),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index == _currentIndex ? Color(0xff5194E1) : Color(0xff28323E),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
