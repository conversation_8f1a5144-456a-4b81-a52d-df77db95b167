import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kurdsat_application/src/core/common/widgets/category_title_section.dart' show CategoryTitleSection;
import '../../../../../core/common/widgets/text_widgets/text_view.dart';
import '../../domain/entities/kurdbin_section.dart';

class VerticalShowDesignWidget extends StatelessWidget {
  final KurdBinSectionEntity sectionEntity;

  const VerticalShowDesignWidget({
    super.key,
    required this.sectionEntity,
  });
  @override
  Widget build(BuildContext context) {
    final hasEnoughShows = sectionEntity.showsList.length >= 2;

    if (!hasEnoughShows) return const SizedBox.shrink();

    final shortenedList = sectionEntity.showsList.take(2).toList();

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.0.r),
      child: Column(
        children: [
          CategoryTitleSection(title: sectionEntity.title),
          16.verticalSpace,
          GridView(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 10,
              childAspectRatio: .75,
            ),
            children: shortenedList.map((show) {
              return AspectRatio(
                aspectRatio: 0.68.r,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6.r),
                  child: Image.network(show.image, fit: BoxFit.cover),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
