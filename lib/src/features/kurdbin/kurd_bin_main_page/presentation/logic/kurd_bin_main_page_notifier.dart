import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import "package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/domain/entities/kurd_bin_main_page_entity.dart" show KurdBinMainPageEntity;
import "package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/domain/repositories/kurd_bin_main_page_repository.dart";

class KurdBinMainPageNotifier extends AsyncNotifier<KurdBinMainPageEntity> {
  late KurdBinMainPageRepository _kurdBinMainPageRepositoryProvider;

  @override
  FutureOr<KurdBinMainPageEntity> build() async {
    _kurdBinMainPageRepositoryProvider = ref.read(kurdBinMainPageRepositoryProvider);
    await fetchData();
    return state.value ?? KurdBinMainPageEntity(categories: [], kurdBinSections: []);
  }

  Future<void> fetchData() async {
    state = const AsyncValue.loading();

    final response = await _kurdBinMainPageRepositoryProvider.fetchKurdBinMainPage();

    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right);
      },
    );
  }
}

final kurdBinMainPageNotifierProvider = AsyncNotifierProvider<KurdBinMainPageNotifier, KurdBinMainPageEntity>(KurdBinMainPageNotifier.new);
