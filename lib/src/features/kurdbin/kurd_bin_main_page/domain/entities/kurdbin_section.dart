import 'package:equatable/equatable.dart';
import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/data/enums/image_orientation_enum.dart';

import 'kurdbin_show_card.dart';

class KurdBinSectionEntity extends Equatable {
  final int id;
  final String title;
  List<KurdbinShowCardEntity> showsList;
  final OrientationEnum orientationEnum;

  KurdBinSectionEntity({
    required this.id,
    required this.title,
    required this.showsList,
    required this.orientationEnum,
  });

  @override
  List<Object?> get props => [title, showsList];
}
