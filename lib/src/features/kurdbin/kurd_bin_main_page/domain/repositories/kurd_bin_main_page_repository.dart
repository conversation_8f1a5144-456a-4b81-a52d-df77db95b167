import 'package:dartz/dartz.dart' show Either;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat_application/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/data/datasources/kurd_bin_remote_data_source.dart' show kurdbinMainPageDatasourceProvider;
import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/data/repositories/kurd_bin_main_page_reposetory_impl.dart' show KurdBinMainPageRepositoryImpl;
import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/domain/entities/kurd_bin_main_page_entity.dart' show KurdBinMainPageEntity;

abstract class KurdBinMainPageRepository {
  Future<Either<ErrorModel, KurdBinMainPageEntity>> fetchKurdBinMainPage();
}

final kurdBinMainPageRepositoryProvider = Provider<KurdBinMainPageRepository>((ref) {
  return KurdBinMainPageRepositoryImpl(kurdBinMainPageRemoteDataSource: ref.read(kurdbinMainPageDatasourceProvider));
});
