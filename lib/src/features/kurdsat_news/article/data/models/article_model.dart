import 'package:json_annotation/json_annotation.dart';
import 'package:kurdsat_application/src/core/utils/helpers/date_formatter.dart';
import '../../domain/entities/article_entity.dart';

part 'article_model.g.dart';

@JsonSerializable()
class ArticleModel {
  final int? id;
  final String? date;
  final String? title;
  final String? body;
  final String? subtitle;
  @JsonKey(name: 'article_categories')
  final ArticleCategoriesModel? articleCategories;
  @Json<PERSON>ey(name: 'article_types')
  final ArticleTypesModel? articleTypes;
  final ArticleImageModel? image;
  final ArticleAuthorModel? author;

  const ArticleModel({
    this.id,
    this.date,
    this.title,
    this.body,
    this.subtitle,
    this.articleCategories,
    this.articleTypes,
    this.image,
    this.author,
  });

  factory ArticleModel.fromJson(Map<String, dynamic> json) => _$ArticleModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleModelToJson(this);

  ArticleEntity toEntity() {
    return ArticleEntity(
      id: id,
      date: parseDateTime(date!),
      title: title,
      body: body,
      subtitle: subtitle,
      articleCategories: articleCategories?.data?.map((e) => e.toEntity()).toList(),
      articleTypes: articleTypes?.data?.map((e) => e.toEntity()).toList(),
      image: image?.toEntity(),
      author: author?.data?.toEntity(),
    );
  }
}

@JsonSerializable()
class ArticleCategoriesModel {
  final List<ArticleCategoryModel>? data;

  const ArticleCategoriesModel({this.data});

  factory ArticleCategoriesModel.fromJson(Map<String, dynamic> json) => _$ArticleCategoriesModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleCategoriesModelToJson(this);
}

@JsonSerializable()
class ArticleCategoryModel {
  final int? id;
  final ArticleCategoryAttributesModel? attributes;

  const ArticleCategoryModel({this.id, this.attributes});

  factory ArticleCategoryModel.fromJson(Map<String, dynamic> json) => _$ArticleCategoryModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleCategoryModelToJson(this);

  ArticleCategoryEntity toEntity() {
    return ArticleCategoryEntity(
      id: id,
      title: attributes?.title,
    );
  }
}

@JsonSerializable()
class ArticleCategoryAttributesModel {
  final String? title;

  const ArticleCategoryAttributesModel({this.title});

  factory ArticleCategoryAttributesModel.fromJson(Map<String, dynamic> json) => _$ArticleCategoryAttributesModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleCategoryAttributesModelToJson(this);
}

@JsonSerializable()
class ArticleTypesModel {
  final List<ArticleTypeModel>? data;

  const ArticleTypesModel({this.data});

  factory ArticleTypesModel.fromJson(Map<String, dynamic> json) => _$ArticleTypesModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleTypesModelToJson(this);
}

@JsonSerializable()
class ArticleTypeModel {
  final int? id;
  final ArticleTypeAttributesModel? attributes;

  const ArticleTypeModel({this.id, this.attributes});

  factory ArticleTypeModel.fromJson(Map<String, dynamic> json) => _$ArticleTypeModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleTypeModelToJson(this);

  ArticleTypeEntity toEntity() {
    return ArticleTypeEntity(
      id: id,
      title: attributes?.title,
    );
  }
}

@JsonSerializable()
class ArticleTypeAttributesModel {
  final String? title;

  const ArticleTypeAttributesModel({this.title});

  factory ArticleTypeAttributesModel.fromJson(Map<String, dynamic> json) => _$ArticleTypeAttributesModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleTypeAttributesModelToJson(this);
}

@JsonSerializable()
class ArticleImageModel {
  final int? id;
  final ArticleImageDataModel? image;

  const ArticleImageModel({this.id, this.image});

  factory ArticleImageModel.fromJson(Map<String, dynamic> json) => _$ArticleImageModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleImageModelToJson(this);

  ArticleImageEntity toEntity() {
    return ArticleImageEntity(
      id: id,
      image: image?.toEntity(),
    );
  }
}

@JsonSerializable()
class ArticleImageDataModel {
  final ArticleImageDataDataModel? data;

  const ArticleImageDataModel({this.data});

  factory ArticleImageDataModel.fromJson(Map<String, dynamic> json) => _$ArticleImageDataModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleImageDataModelToJson(this);

  ArticleImageDataEntity toEntity() {
    return ArticleImageDataEntity(
      id: data?.id,
      url: data?.attributes?.url,
    );
  }
}

@JsonSerializable()
class ArticleImageDataDataModel {
  final int? id;
  final ArticleImageAttributesModel? attributes;

  const ArticleImageDataDataModel({this.id, this.attributes});

  factory ArticleImageDataDataModel.fromJson(Map<String, dynamic> json) => _$ArticleImageDataDataModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleImageDataDataModelToJson(this);
}

@JsonSerializable()
class ArticleImageAttributesModel {
  final String? url;

  const ArticleImageAttributesModel({this.url});

  factory ArticleImageAttributesModel.fromJson(Map<String, dynamic> json) => _$ArticleImageAttributesModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleImageAttributesModelToJson(this);
}

@JsonSerializable()
class ArticleAuthorModel {
  final ArticleAuthorDataModel? data;

  const ArticleAuthorModel({this.data});

  factory ArticleAuthorModel.fromJson(Map<String, dynamic> json) => _$ArticleAuthorModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleAuthorModelToJson(this);
}

@JsonSerializable()
class ArticleAuthorDataModel {
  final int? id;
  final ArticleAuthorAttributesModel? attributes;

  const ArticleAuthorDataModel({this.id, this.attributes});

  factory ArticleAuthorDataModel.fromJson(Map<String, dynamic> json) => _$ArticleAuthorDataModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleAuthorDataModelToJson(this);

  ArticleAuthorEntity toEntity() {
    return ArticleAuthorEntity(
      id: id,
      fullName: attributes?.fullName,
    );
  }
}

@JsonSerializable()
class ArticleAuthorAttributesModel {
  @JsonKey(name: 'full_name')
  final String? fullName;

  const ArticleAuthorAttributesModel({this.fullName});

  factory ArticleAuthorAttributesModel.fromJson(Map<String, dynamic> json) => _$ArticleAuthorAttributesModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleAuthorAttributesModelToJson(this);
}

@JsonSerializable()
class ArticleResponseModel {
  final ArticleDataModel? data;
  final Map<String, dynamic>? meta;

  const ArticleResponseModel({this.data, this.meta});

  factory ArticleResponseModel.fromJson(Map<String, dynamic> json) => _$ArticleResponseModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleResponseModelToJson(this);

  ArticleResponseEntity toEntity() {
    return ArticleResponseEntity(
      data: data?.toEntity(),
      meta: meta,
    );
  }
}

@JsonSerializable()
class ArticleDataModel {
  final int? id;
  final ArticleModel? attributes;

  const ArticleDataModel({this.id, this.attributes});

  factory ArticleDataModel.fromJson(Map<String, dynamic> json) => _$ArticleDataModelFromJson(json);
  Map<String, dynamic> toJson() => _$ArticleDataModelToJson(this);

  ArticleEntity toEntity() {
    return ArticleEntity(
      id: id,
      date: parseDateTime(attributes?.date),
      title: attributes?.title,
      body: attributes?.body,
      subtitle: attributes?.subtitle,
      articleCategories: attributes?.articleCategories?.data?.map((e) => e.toEntity()).toList(),
      articleTypes: attributes?.articleTypes?.data?.map((e) => e.toEntity()).toList(),
      image: attributes?.image?.toEntity(),
      author: attributes?.author?.data?.toEntity(),
    );
  }
}


// DateTime?