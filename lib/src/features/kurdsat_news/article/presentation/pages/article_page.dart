import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart' show Html, Style, FontSize;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat_application/src/core/common/widgets/loading_view.dart';
import 'package:kurdsat_application/src/core/common/widgets/text_widgets/text_view.dart';
import 'package:kurdsat_application/src/core/constants/const.dart';
import 'package:kurdsat_application/src/core/extensions/date_formatter.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/article/presentation/logic/article_notifier.dart';
import 'package:share_plus/share_plus.dart';

import '../../domain/entities/article_entity.dart';

class ArticlePage extends ConsumerStatefulWidget {
  final int articleId;

  const ArticlePage({super.key, required this.articleId});

  @override
  ConsumerState<ArticlePage> createState() => _ArticlePageState();
}

class _ArticlePageState extends ConsumerState<ArticlePage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(articleNotifierProvider.notifier).fetchArticle(widget.articleId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final articleState = ref.watch(articleNotifierProvider);

    return Scaffold(
      backgroundColor: const Color(0xff0A0C10),
      appBar: AppBar(
        backgroundColor: const Color(0xff14191F),
        elevation: 0,
        leading: context.canPop()
            ? BackButton(
                color: Colors.white,
                onPressed: () {
                  context.pop();
                },
              )
            : null,
        title: TextView(
          text: 'Article',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
      ),
      body: articleState.when(
        loading: () => const Center(
          child: LoadingView(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              16.verticalSpace,
              TextView(
                text: 'Failed to load article',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
              8.verticalSpace,
              TextView(
                text: error.toString(),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlignment: TextAlign.center,
              ),
              16.verticalSpace,
              ElevatedButton(
                onPressed: () {
                  // ref.read(articleNotifierProvider.notifier).fetchArticle(widget.articleId);
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
        data: (ArticleEntity? article) {
          // inspect(article?.articleCategories.first.title ?? "");
          if (article == null) {
            return Center(
              child: TextView(
                text: 'Article not found',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            );
          }

          inspect(article);

          return SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                16.verticalSpace,

                // _buildBreadcrumb(article.articleCategories?.first.title ?? "Article"),
                // _buildBreadcrumb(article.articleTypes?.first.title ?? "Article"),
                _buildBreadcrumb((article.articleTypes?.isNotEmpty == true) ? article.articleTypes?.first.title ?? "Article" : "Article"),
                16.verticalSpace,

                // Title
                if (article.title != null)
                  TextView(
                    text: article.title!,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                16.verticalSpace,

                // Image
                _buildArticleImage(article.image?.image?.url ?? ""),

                16.verticalSpace,

                // Author, Date, Share Row
                _buildAuthorDateShareRow(article),

                24.verticalSpace,

                // Subtitle
                if (article.subtitle != null && article.subtitle!.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextView(
                        text: article.subtitle!,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xffEFF2F5),
                        ),
                      ),
                      16.verticalSpace,
                    ],
                  ),

                // Body (HTML content - simplified as text for now)
                // if (article.body != null) _buildBodyContent(article.body!),
                if (article.body != null)
                  Html(
                    data: article.body!,
                    style: {
                      "body": Style(
                        fontSize: FontSize(16),
                        color: Color(0xffEFF2F5),
                        // lineHeight: const LineHeight(1.6),
                      ),
                    },
                  ),

                32.verticalSpace,
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBreadcrumb(String categoryTitle) {
    return Row(
      children: [
        TextView(
          text: 'Kurdsat News',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xffA2B0C3),
          ),
        ),
        2.horizontalSpace,
        Icon(
          Icons.chevron_right,
          size: 24,
          color: Color(0xff28323E),
        ),
        2.horizontalSpace,
        TextView(
          text: categoryTitle,
          style: TextStyle(
            fontSize: 14,
            color: Color(0xffA2B0C3),
          ),
        ),
      ],
    );
  }

  Widget _buildArticleImage(String imageUrl) {
    return Container(
      width: double.maxFinite,
      height: 200.h,
      clipBehavior: Clip.antiAlias,
      decoration: ShapeDecoration(
        shape: ContinuousRectangleBorder(
          borderRadius: BorderRadius.circular(24.0),
        ),
      ),
      child: Image.network(
        '$kBaseAssetUrl$imageUrl',
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey[800],
            child: const Center(
              child: Icon(
                Icons.image_not_supported,
                color: Colors.grey,
                size: 48,
              ),
            ),
          );
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            color: Colors.grey[800],
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAuthorDateShareRow(ArticleEntity article) {
    return Row(
      children: [
        // Author
        Expanded(
          child: Row(
            children: [
              SvgPicture.asset(
                'assets/icons/pen.svg',
                width: 24,
                height: 24,
              ),
              4.horizontalSpace,
              Expanded(
                child: TextView(
                  text: article.author?.fullName?.trim() ?? "Kurdsat News",
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xffA2B0C3),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),

        // Date
        Expanded(
          child: Row(
            // mainAxisAlignment: article.author?.fullName == null ? MainAxisAlignment.start : MainAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                'assets/icons/calender.svg',
                width: 24,
                height: 24,
              ),
              4.horizontalSpace,
              TextView(
                text: (article.date ?? DateTime.now()).getSmartRelativeTime(),
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xffA2B0C3),
                ),
              ),
            ],
          ),
        ),

        Expanded(
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              _shareArticle(article);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SvgPicture.asset(
                  'assets/icons/share.svg',
                  width: 24,
                  height: 24,
                ),
                8.horizontalSpace,
                TextView(
                  text: 'Share',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xffA2B0C3),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _shareArticle(ArticleEntity article) {
    final ShareParams shareParams = ShareParams(
      title: article.title ?? 'Article',
      subject: 'Check out this article from Kurdsat News',
      uri: Uri.parse('https://kurdsat.tv/articles/${widget.articleId}'),
    );

    SharePlus.instance.share(shareParams);
  }
}
