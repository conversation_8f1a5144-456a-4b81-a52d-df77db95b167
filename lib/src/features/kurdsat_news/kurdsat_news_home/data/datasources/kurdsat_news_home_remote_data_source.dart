import 'dart:convert' show json;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat_application/src/core/constants/const.dart';
import 'package:kurdsat_application/src/core/utils/managers/http/http_methods.dart' show HttpMethods;
import 'package:kurdsat_application/src/core/api/api.dart' show Api;
import 'package:kurdsat_application/src/core/utils/managers/http/http_manager.dart' show HttpManager, httpManagerProvider;

class KurdsatNewsHomeRemoteDataSource {
  final HttpManager httpManager;

  KurdsatNewsHomeRemoteDataSource({required this.httpManager});

  Future<Map<String, dynamic>> fetchSection() async {
    final response = await httpManager.request(
      path: Api().kurdsatNewsHomePage,
      method: HttpMethods.get,
      headers: {
        "Authorization": "Bearer $token",
      },
    );

    return json.decode(response.data as String) as Map<String, dynamic>;
  }
}

final sectionDatasourceProvider = Provider<KurdsatNewsHomeRemoteDataSource>((ref) {
  return KurdsatNewsHomeRemoteDataSource(httpManager: ref.read(httpManagerProvider));
});
