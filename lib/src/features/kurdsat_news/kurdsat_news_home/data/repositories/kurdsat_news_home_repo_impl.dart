import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:kurdsat_application/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat_application/src/core/utils/helpers/error_parser.dart' show errorParser;
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/data/datasources/kurdsat_news_home_remote_data_source.dart' show KurdsatNewsHomeRemoteDataSource;
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/data/mappers/kurdsat_news_home_response_mapper.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/data/models/kurdsat_news_home_response_model.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/domain/entities/kurdsat_news_home_response_entity.dart' show KurdsatNewsHomeResponseEntity;
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/domain/repositories/kurdsat_news_home_reposetory.dart' show KurdsatNewsHomeRepository;

class KurdsatNewsHomeRepoImpl implements KurdsatNewsHomeRepository {
  final KurdsatNewsHomeRemoteDataSource sectionRemoteDataSource;

  KurdsatNewsHomeRepoImpl({required this.sectionRemoteDataSource});

  @override
  Future<Either<ErrorModel, KurdsatNewsHomeResponseEntity>> fetchSectionsData() async {
    try {
      final result = await sectionRemoteDataSource.fetchSection();

      // log("result");
      // inspect(result);

      KurdsatNewsHomeResponseModel model = KurdsatNewsHomeResponseModel.fromJson(result);

      // log("model");
      // inspect(model);
      KurdsatNewsHomeResponseMapper mapper = KurdsatNewsHomeResponseMapper();

      // log("mapper");
      // inspect(mapper.toEntity(model));

      final KurdsatNewsHomeResponseEntity kurdsatNewsHomeResponseEntity = mapper.toEntity(model);

      // log("kurdsatNewsHomeResponseEntity");
      // inspect(kurdsatNewsHomeResponseEntity);

      return Right(kurdsatNewsHomeResponseEntity);
    } catch (error, stackTrace) {
      log(error.toString(), stackTrace: stackTrace);
      return Left(errorParser(error, stackTrace));
    }
  }
}
