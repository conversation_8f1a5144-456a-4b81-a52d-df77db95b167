import 'package:json_annotation/json_annotation.dart' show JsonSerializable, JsonKey;
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/data/models/image/image_model.dart';

part 'parent_image_model.g.dart';

@JsonSerializable()
class ParentImageModel {
  @JsonKey(name: 'id')
  final int? id;

  @JsonKey(name: 'image')
  final ImageModel? image;

  const ParentImageModel({
    this.id,
    this.image,
  });

  factory ParentImageModel.fromJson(Map<String, dynamic> json) => _$ParentImageModelFromJson(json);

  Map<String, dynamic> toJson() => _$ParentImageModelToJson(this);
}
