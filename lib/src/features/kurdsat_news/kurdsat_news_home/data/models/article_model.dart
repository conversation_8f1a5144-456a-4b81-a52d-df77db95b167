import 'package:json_annotation/json_annotation.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/data/models/author_model.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/data/models/image/parent_image_model.dart';

part 'article_model.g.dart';

@JsonSerializable()
class ArticleModel {
  @JsonKey(name: 'id')
  final int? id;

  @Json<PERSON>ey(name: 'title')
  final String? title;

  @JsonKey(name: 'publishedAt')
  final String? publishedAt;

  @JsonKey(name: 'image')
  final ParentImageModel? image;

  @Json<PERSON>ey(name: 'author')
  final AuthorModel? author;

  const ArticleModel({
    this.id,
    this.title,
    this.publishedAt,
    this.image,
    this.author,
  });

  factory ArticleModel.fromJson(Map<String, dynamic> json) => _$ArticleModelFromJson(json);

  Map<String, dynamic> toJson() => _$ArticleModelToJson(this);
}
