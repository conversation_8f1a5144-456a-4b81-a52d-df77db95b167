import 'package:json_annotation/json_annotation.dart' show JsonSerializable, JsonKey;
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/data/models/article_category_model.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/data/models/article_model.dart' show ArticleModel;

part 'kurdsat_news_home_data_model.g.dart';

@JsonSerializable()
class KurdsatNewsHomeDataModel {
  @Json<PERSON><PERSON>(name: 'id')
  final int? id;

  @Json<PERSON>ey(name: 'title')
  final String? title;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'value')
  final String? value;

  @Json<PERSON><PERSON>(name: 'order')
  final int? order;

  @Json<PERSON>ey(name: 'view_mode')
  final String? viewMode;

  @Json<PERSON>ey(name: 'locale')
  final String? locale;

  @JsonKey(name: 'article_categories')
  final List<ArticleCategoryModel>? articleCategories;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'articles')
  final List<ArticleModel>? articles;

  const KurdsatNewsHomeDataModel({
    this.id,
    this.title,
    this.value,
    this.order,
    this.viewMode,
    this.locale,
    this.articleCategories,
    this.articles,
  });

  factory KurdsatNewsHomeDataModel.fromJson(Map<String, dynamic> json) => _$KurdsatNewsHomeDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$KurdsatNewsHomeDataModelToJson(this);
}
