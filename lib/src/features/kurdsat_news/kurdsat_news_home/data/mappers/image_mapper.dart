import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/data/models/image/parent_image_model.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/domain/entities/image_entity.dart';

class ImageMapper {
  static ImageEntity toEntity(ParentImageModel? model) {
    if (model == null) return const ImageEntity();

    return ImageEntity(
      id: model.image?.id,
      url: model.image?.url,
    );
  }
}
