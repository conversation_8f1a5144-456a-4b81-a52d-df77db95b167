// lib/mappers/news_response_mapper.dart

import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/data/models/author_model.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/domain/entities/author_entity.dart';

class AuthorMapper {
  static AuthorEntity toEntity(AuthorModel? model) {
    if (model == null) return const AuthorEntity();

    return AuthorEntity(
      id: model.id,
      fullName: model.fullName,
    );
  }
}
