import 'package:dartz/dartz.dart' show Either;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat_application/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/data/datasources/kurdsat_news_home_remote_data_source.dart' show sectionDatasourceProvider;
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/data/repositories/kurdsat_news_home_repo_impl.dart' show KurdsatNewsHomeRepoImpl;
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/domain/entities/kurdsat_news_home_response_entity.dart';

abstract class KurdsatNewsHomeRepository {
  Future<Either<ErrorModel, KurdsatNewsHomeResponseEntity>> fetchSectionsData();
}

final sectionRepositoryProvider = Provider<KurdsatNewsHomeRepository>((ref) {
  return KurdsatNewsHomeRepoImpl(sectionRemoteDataSource: ref.read(sectionDatasourceProvider));
});
