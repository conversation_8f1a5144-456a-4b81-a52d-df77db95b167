// lib/entities/news_response_entity.dart

import 'package:equatable/equatable.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_category_entity.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_entity.dart';

class KurdsatNewsHomeDataEntity extends Equatable {
  final int? id;
  final String? title;
  final String? value;
  final int? order;
  final String viewMode;
  final String? locale;
  final List<ArticleCategoryEntity> articleCategories;
  final List<ArticleEntity> articles;

  const KurdsatNewsHomeDataEntity({
    this.id,
    this.title,
    this.value,
    this.order,
    required this.viewMode,
    this.locale,
    required this.articleCategories,
    required this.articles,
  });

  @override
  List<Object?> get props => [id, title, value, order, viewMode, locale, articleCategories, articles];
}
