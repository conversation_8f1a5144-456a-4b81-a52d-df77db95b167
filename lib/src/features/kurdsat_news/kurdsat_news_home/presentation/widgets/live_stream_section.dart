import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:kurdsat_application/src/core/common/widgets/text_widgets/text_view.dart';

class LiveStreamSection extends StatelessWidget {
  const LiveStreamSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 16 / 9,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white12,
              Colors.black45,
            ],
          ),
        ),
        child: Stack(
          children: [
            Image.asset(
              "assets/images/liveThumbnailPlaceholder.png",
              fit: BoxFit.cover,
            ),

            Positioned.fill(
              child: Container(
                // background: linear-gradient(180deg, rgba(20, 20, 20, 0) 0%, #141414 100%);
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Color(0xff141414),
                    ],
                  ),
                ),
              ),
            ),

            Center(
              child: Icon(
                Icons.play_circle_outline_rounded,
                size: 60.sp,
                color: Colors.white,
              ),
            ),
            Positioned(
              top: 20.r,
              left: 20.r,
              child: SvgPicture.asset(
                "assets/icons/live_icon.svg",
                fit: BoxFit.cover,
              ),
            ),

            Positioned(
              bottom: 20.r,
              left: 20.r,
              child: TextView(
                text: 'Live Stream',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
