import 'package:carousel_slider/carousel_options.dart' show CarouselOptions;
import 'package:carousel_slider/carousel_slider.dart' show CarouselSlider;
import 'package:flutter/cupertino.dart' show CupertinoIcons, CupertinoButton;
import 'package:flutter/material.dart' show AnimatedContainer, BoxDecoration, BoxShape, BuildContext, Color, Colors, Column, Curves, Divider, EdgeInsets, Expanded, FontWeight, Icon, Icons, MainAxisAlignment, MainAxisSize, Navigator, Padding, Row, Size, State, StatefulWidget, Text, TextStyle, Widget;
import 'package:flutter/services.dart' show HapticFeedback;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat_application/src/core/common/widgets/text_widgets/text_view.dart' show TextView;
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_category_entity.dart' show ArticleCategoryEntity;
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/domain/entities/article_entity.dart' show ArticleEntity;
import 'package:kurdsat_application/src/core/common/widgets/news_card/news_card_view.dart' show NewsCardView;
import 'package:kurdsat_application/src/core/common/widgets/news_card/news_card_view_preview.dart' show NewsCardViewPreview;
import 'package:kurdsat_application/src/core/common/widgets/context_menu/context_menu_action.dart' show CustomContextMenuAction;
import 'package:kurdsat_application/src/core/common/widgets/context_menu/custom_context_menu.dart' show CustomContextMenu;

import '../../../../../core/constants/const.dart' show kBaseAssetUrl;

class ArticleCategory extends StatefulWidget {
  final ArticleCategoryEntity category;

  const ArticleCategory({super.key, required this.category});

  @override
  State<ArticleCategory> createState() => _ArticleCategoryState();
}

class _ArticleCategoryState extends State<ArticleCategory> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            children: [
              CupertinoButton(
                onPressed: () {},
                padding: EdgeInsets.zero,
                minimumSize: Size(0, 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextView(
                      text: widget.category.title ?? "Unknown",
                      style: TextStyle(
                        color: Color(0xffEFF2F5).withValues(alpha: 0.7),
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Icon(
                      CupertinoIcons.arrow_up_right,
                      size: 24.r,
                      color: Color(0xff3693FF).withValues(alpha: 0.7),
                    ),
                  ],
                ),
              ),
              12.verticalSpace,
              Divider(color: const Color(0xff28323E), height: 1, thickness: 1),
            ],
          ),
        ),
        16.verticalSpace,
        CarouselSlider(
          options: CarouselOptions(
            height: 300.r,
            viewportFraction: 0.92,
            pageSnapping: true,
            enableInfiniteScroll: false,
            onPageChanged: (index, reason) {
              HapticFeedback.mediumImpact();
              setState(() {
                _currentIndex = index;
              });
            },
            // pauseAutoPlayOnTouch: true,
            // autoPlayCurve: Curves.fastOutSlowIn,
            // autoPlay: true,
            // autoPlayInterval: const Duration(seconds: 15),
            // enlargeCenterPage: true,
            // enlargeStrategy: CenterPageEnlargeStrategy.zoom,
          ),
          items: widget.category.articles.map(
            (ArticleEntity article) {
              return CustomContextMenu(
                previewBuilder: (context, animation, child) {
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Expanded(
                        child: NewsCardViewPreview(
                          imageUrl: "https://test-kurdsatnews-portal.rndlabs.dev${article.image?.url ?? ""}",
                          dutation: 50,
                          title: article.title ?? "",
                        ),
                      ),
                    ],
                  );
                },

                isSelected: true,
                onPress: () {
                  context.pop();
                },
                actions: [
                  // harkate dasanem ba actiony har itemka shshalay rashabe,
                  // tasawrakam hokar bottom menuaka be ale set state bangakay la backward aniumation kate
                  // objectaka bwny nia la asla abe ama chara bkre.......
                  // agar bawana chara nakra tanha wa bkam actions requrie nabe w bas agina hich keshayaky nya.
                  Text(
                    "data",
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 16.sp,
                    ),
                  ),
                  CustomContextMenuAction(
                    trailingIcon: Icons.remove_circle_outline_rounded,
                    isDestructiveAction: true,
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(
                      "هەڵبژاردن",
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                  CustomContextMenuAction(
                    trailingIcon: Icons.share_outlined,

                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(
                      "هاوبەشکردن",
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                ],

                child: NewsCardView(
                  imageUrl: "$kBaseAssetUrl${article.image?.url ?? ""}",
                  dutation: 10,
                  title: article.title ?? "",
                ),
              );
            },
          ).toList(),
        ),
        16.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            widget.category.articles.length,
            (index) => AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.fastOutSlowIn,

              width: 8.r,
              height: 8.r,
              margin: EdgeInsets.symmetric(horizontal: 4.r),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index == _currentIndex ? Color(0xff5194E1) : Color(0xff28323E),
              ),
            ),
          ),
        ),
        32.verticalSpace,
      ],
    );
  }
}
