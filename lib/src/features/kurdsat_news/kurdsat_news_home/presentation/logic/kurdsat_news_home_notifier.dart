import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/domain/entities/kurdsat_news_home_response_entity.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/domain/repositories/kurdsat_news_home_reposetory.dart';

class KurdsatNewsHomeNotifier extends AsyncNotifier<KurdsatNewsHomeResponseEntity> {
  late KurdsatNewsHomeRepository _sectionRepositoryProvider;

  @override
  FutureOr<KurdsatNewsHomeResponseEntity> build() async {
    _sectionRepositoryProvider = ref.read(sectionRepositoryProvider);
    await fetchProducts();
    return state.value ?? const KurdsatNewsHomeResponseEntity();
  }

  Future<void> fetchProducts() async {
    state = const AsyncValue.loading();

    final response = await _sectionRepositoryProvider.fetchSectionsData();

    response.fold(
      (left) {
        state = AsyncValue.error(left, StackTrace.fromString(left.error?.message ?? ""));
      },
      (right) {
        state = AsyncValue.data(right);
      },
    );
  }
}

final sectionNotifierProvider = AsyncNotifierProvider<KurdsatNewsHomeNotifier, KurdsatNewsHomeResponseEntity>(KurdsatNewsHomeNotifier.new);
