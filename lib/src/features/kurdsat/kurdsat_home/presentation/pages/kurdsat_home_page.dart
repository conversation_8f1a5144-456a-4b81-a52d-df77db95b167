import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat_application/src/app/routes/router.dart';
import 'package:kurdsat_application/src/core/common/widgets/text_widgets/text_view.dart' show TextView;

import 'package:kurdsat_application/src/features/kurdsat/kurdsat_home/presentation/logic/kurdsat_home_notifier.dart' show kurdsatHomeNotifierProvider;

class KurdsatHomePage extends ConsumerStatefulWidget {
  const KurdsatHomePage({super.key});

  @override
  ConsumerState<KurdsatHomePage> createState() => _KurdsatHomePageState();
}

class _KurdsatHomePageState extends ConsumerState<KurdsatHomePage> {
  @override
  Widget build(BuildContext context) {
    // final kurdsatHomeState = ref.watch(kurdsatHomeNotifierProvider);
    return Scaffold(
      backgroundColor: Color(0xff0A0C10),
      appBar: AppBar(
        backgroundColor: Color(0xff14191F),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            color: Color(0xffE9F2FB),
            onPressed: () {
              // Handle search
            },
          ),
        ],
        title: const TextView(
          text: "Kurdsat",
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: false,
        elevation: 0,
      ),

      body: DefaultTextStyle(
        style: TextStyle(
          color: Color(0xffE9F2FB),
          fontSize: 16.sp,
          fontWeight: FontWeight.bold,
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                24.verticalSpace,

                // // Continue
                // kurdsatHomeState.maybeWhen(
                //   orElse: () => Text("Loading..."),
                //   data: (data) => Text("Data loaded: ${data.length} items"),
                //   loading: () => CircularProgressIndicator(),
                //   error: (error, stack) => Text("Error: $error"),
                // ),
                Text(
                  "Kurdsat Home Page",
                  style: TextStyle(
                    color: Color(0xffE9F2FB),
                    fontSize: 24.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                24.verticalSpace,

                GridView(
                  physics: NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 3, crossAxisSpacing: 10),
                  children: [
                    InkWell(
                      onTap: () {
                        context.push("/kurdsat-news");
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Color(0xff14191f),
                          borderRadius: BorderRadius.circular(10.r),
                        ),
                        child: Center(
                          child: TextView(
                            text: "Kurdsat\nNews",
                            textAlignment: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        context.push("/kurdsat");
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Color(0xff14191f),
                          borderRadius: BorderRadius.circular(10.r),
                        ),
                        child: Center(child: Text("Kurdsat")),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        context.pushNamed(RouteNameEnum.kurdbinHomePage.name);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Color(0xff14191f),
                          borderRadius: BorderRadius.circular(10.r),
                        ),
                        child: Center(child: Text("Kurdbin")),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
