import 'package:go_router/go_router.dart' show GoRout<PERSON>, GoRouter;
import 'package:kurdsat_application/src/features/common/live_broadcast/presentation/pages/live_broadcast_page.dart' show LiveBroadcastPage;
import 'package:kurdsat_application/src/features/common/splash/presentation/pages/splash_page.dart';
import 'package:kurdsat_application/src/features/kurdbin/kurd_bin_main_page/presentation/pages/kurd_bin_main_page_page.dart';
import 'package:kurdsat_application/src/features/kurdsat/kurdsat_home/presentation/pages/kurdsat_home_page.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/kurdsat_news_home/presentation/pages/kurdsat_news_home_page.dart';
import 'package:kurdsat_application/src/features/kurdsat_news/article/presentation/pages/article_page.dart';

enum RouteNameEnum {
  splash,
  kurdsatNewsHome,
  article,
  kurdbinHomePage,
  kurdsatHome,
  liveBroadcast,
}

final GoRouter router = GoRouter(
  // initialLocation: "/kurdbin_home_page",
  // initialLocation: "/kurdsat-news",
  initialLocation: "/live-broadcast",
  // initialLocation: "/kurdsat",
  //
  routes: [
    // General Routes
    GoRoute(
      name: RouteNameEnum.splash.name,
      path: "/splash",
      builder: (context, state) => SplashPage(),
    ),

    //
    //
    //
    //   _  __             _           _     _   _
    //  | |/ /            | |         | |   | \ | |
    //  | ' /_   _ _ __ __| |___  __ _| |_  |  \| | _____      _____
    //  |  <| | | | '__/ _` / __|/ _` | __| | . ` |/ _ \ \ /\ / / __|
    //  | . \ |_| | | | (_| \__ \ (_| | |_  | |\  |  __/\ V  V /\__ \
    //  |_|\_\__,_|_|  \__,_|___/\__,_|\__| |_| \_|\___| \_/\_/ |___/
    GoRoute(
      name: RouteNameEnum.kurdsatNewsHome.name,
      path: "/kurdsat-news",
      builder: (context, state) => KurdsatNewsHomePage(),
    ),
    GoRoute(
      name: RouteNameEnum.article.name,
      path: "/article/:id",
      builder: (context, state) {
        final articleId = int.tryParse(state.pathParameters['id'] ?? '');
        if (articleId == null) {
          return const KurdsatNewsHomePage();
        }
        return ArticlePage(articleId: articleId);
      },
    ),
    GoRoute(
      name: RouteNameEnum.liveBroadcast.name,
      path: "/live-broadcast",
      builder: (context, state) => LiveBroadcastPage(),
    ),

    //
    //
    //
    //   _  __             _ _     _
    //  | |/ /            | | |   (_)
    //  | ' /_   _ _ __ __| | |__  _ _ __
    //  |  <| | | | '__/ _` | '_ \| | '_ \
    //  | . \ |_| | | | (_| | |_) | | | | |
    //  |_|\_\__,_|_|  \__,_|_.__/|_|_| |_|
    GoRoute(
      name: RouteNameEnum.kurdbinHomePage.name,
      path: "/kurdbin_home_page",
      builder: (context, state) => KurdBinMainPagePage(),
    ),

    //
    //
    //
    //   _  __             _           _
    //  | |/ /            | |         | |
    //  | ' /_   _ _ __ __| |___  __ _| |_
    //  |  <| | | | '__/ _` / __|/ _` | __|
    //  | . \ |_| | | | (_| \__ \ (_| | |_
    //  |_|\_\__,_|_|  \__,_|___/\__,_|\__|
    GoRoute(
      name: RouteNameEnum.kurdsatHome.name,
      path: "/kurdsat",
      builder: (context, state) => KurdsatHomePage(),
    ),
  ],
);
