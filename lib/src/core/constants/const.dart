import 'package:flutter/material.dart';

const kKu = 'ar';
const kAr = 'ar';
const kEn = 'en';

const kEG = 'EG';
const kIQ = 'IQ';
const kUS = 'US';

const String kPhoneNumber = '000000000';

const String kAndroidURL = "";
const String kIosURL = "";
const String kHuaweiURL = "";

const double kButtonHeight = 40.0;

const BorderRadius borderRadius = BorderRadius.all(
  Radius.circular(8),
);

const String token = "59a4ee100915bc3dcdc2dc13d486af460e6f59b022f9a8d71c5db53b164cbf160bf0e2d0fb119f7c9cd2ba640516e4fa9a010a84c0222c1e5378f914e1bf51beba84f6c5a12b6784f75cc3523c30e7224c65dca4b5687f6b9152185a8d70a47118611ccc0ec989043ce0eb45ea508af38ffe1e07ceb3bc930e2300cf28acc798";

const String kBaseAssetUrl = "https://test-kurdsatnews-portal.rndlabs.dev";
const String kBaseKurdsatNewsUrl = "$kBaseAssetUrl/api";
