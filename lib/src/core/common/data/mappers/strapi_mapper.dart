// Generic Strapi API Response Pattern

import 'package:equatable/equatable.dart';
import 'package:kurdsat_application/src/core/common/data/models/general_model.dart';
import 'package:kurdsat_application/src/core/common/domain/entities/strapi_response_entity.dart';

abstract class StrapiMapper<TModel, TEntity extends Equatable> {
  // Convert model to entity
  TEntity toEntity(TModel model);

  // Convert single response
  StrapiResponseEntity<TEntity> mapResponseToEntity(StrapiResponse<TModel> response) {
    return StrapiResponseEntity(
      data: StrapiItemEntity(
        id: response.data.id,
        attributes: toEntity(response.data.attributes),
      ),
      meta: response.meta,
    );
  }

  // Convert list response
  StrapiListResponseEntity<TEntity> mapListResponseToEntity(StrapiListResponse<TModel> response) {
    return StrapiListResponseEntity(
      data: response.data
          .map(
            (item) => StrapiItemEntity(
              id: item.id,
              attributes: toEntity(item.attributes),
            ),
          )
          .toList(),
      meta: response.meta,
    );
  }
}
