import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../app/logic/app_settings.dart';
import '../../../../app/theme/app_theme.dart';
import '../../../../app/theme/colors.dart';
import '../../../../injection.dart';
import '../../domain/entities/language.dart';
import '../button_view.dart';
import '../text_widgets/text_view.dart';

class AppBarView extends StatelessWidget implements PreferredSizeWidget {
  final String? appBarTitle;
  final TextStyle? appBarTitleStyle;
  final Widget? customeOption;
  final bool showAppbarIcon;
  final bool showLanguageIcon;
  final bool showContactUsIcon;
  final Color? appBarColor;
  final bool showDrawer;

  const AppBarView({super.key, this.appBarTitle, this.customeOption, this.appBarTitleStyle, this.showAppbarIcon = false, this.showLanguageIcon = false, this.showContactUsIcon = false, this.appBarColor, this.showDrawer = false});

  /// CONTAINS LEADING ARROWBACK BUTTON WITH TITLE BACK
  factory AppBarView.onlyBackButton() => const AppBarWidget();

  void _showLanguageDialog(BuildContext context) {
    final appSettings = serviceLocator<AppSettings>();
    final currentLocale = context.locale;
    final colorScheme = Theme.of(context).colorScheme;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.language, color: colorScheme.primary),
            const SizedBox(width: 12),
            const TextView(
              text: 'select_language',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: appSettings.languages.languagesData.map((language) {
            final isSelected = language.local.countryCode == currentLocale.countryCode;

            return ListTile(
              leading: CircleAvatar(
                backgroundColor: isSelected ? colorScheme.primaryContainer : colorScheme.surfaceContainerHighest,
                child: TextView(
                  text: language.shortDisplayLabel,
                  style: TextStyle(color: isSelected ? colorScheme.primary : colorScheme.onSurfaceVariant, fontWeight: FontWeight.bold),
                ),
              ),
              title: TextView(
                text: language.fullDisplayLabel,
                style: TextStyle(fontWeight: isSelected ? FontWeight.bold : FontWeight.normal),
              ),
              subtitle: TextView(text: getLanguageName(language)),
              selected: isSelected,
              selectedTileColor: colorScheme.primaryContainer.withValues(alpha: .1),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              onTap: () {
                // Keep the real locale for translations
                context.setLocale(language.local);
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
        actions: [ButtonView(semanticLabelValue: "general_cancel", onClick: () => Navigator.pop(context), title: 'general_cancel', buttonType: ButtonType.textButton)],
      ),
    );
  }

  String getLanguageName(Language language) {
    // Use a map for cleaner language code to name mapping
    final languageCountryNames = {'US': 'English', 'AE': 'کوردی', 'IQ': 'العربية'};

    // Return the language name or a default if not found
    return languageCountryNames[language.local.countryCode] ?? language.fullDisplayLabel;
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: appBarColor,
      centerTitle: true,
      elevation: 1.0,
      leading: leadingView(context),
      // leadingWidth: showDrawer ? kToolbarHeight : 100,
      titleSpacing: 0.0,
      title: TextView(text: appBarTitle ?? ""),
      actions: [
        if (showContactUsIcon)
          IconButton(
            icon: const Icon(Icons.contact_support_outlined),
            onPressed: () {
              // Handle contact us
            },
          ),
        if (showLanguageIcon)
          IconButton(
            icon: const Icon(Icons.language),
            tooltip: 'change_language'.tr(),
            onPressed: () => _showLanguageDialog(context),
          ),
      ],
    );
  }

  Widget leadingView(BuildContext context) {
    if (showDrawer) {
      return IconButton(
        icon: const Icon(Icons.menu),
        onPressed: () {
          FocusScope.of(context).requestFocus(FocusNode());
          Scaffold.of(context).openDrawer();
        },
      );
    } else {
      return const BackButton();
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class AppBarWidget extends AppBarView {
  const AppBarWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: const SizedBox.shrink(),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // if (context.router.canPop()) ...[
          if (true) ...[
            TextButton.icon(
              icon: const Icon(Icons.arrow_back_ios_new_rounded),
              onPressed: () {
                // context.router.pop
              },
              label: TextView(
                text: "general_back",
                style: Theme.of(context).textTheme.titleMedium?.copyWith(color: ColorPalette.black, fontSize: kFontSizeLG, fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ],
      ),
      leadingWidth: 0,
      titleSpacing: 0,
      centerTitle: false,
    );
  }
}
