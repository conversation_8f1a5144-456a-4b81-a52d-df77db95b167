import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:kurdsat_application/src/core/common/widgets/text_widgets/text_view.dart' show TextView;

class NewsTileView extends StatelessWidget {
  final String imageUrl;
  final String title;
  final String subtitle;
  final int? articleId;
  final VoidCallback? onTap;

  const NewsTileView({
    super.key,
    required this.imageUrl,
    required this.title,
    required this.subtitle,
    required this.articleId,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        if (articleId != null) {
          context.push('/article/$articleId');
        } else if (onTap != null) {
          onTap!();
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        width: double.maxFinite,
        clipBehavior: Clip.antiAlias,
        decoration: ShapeDecoration(
          color: const Color(0xFF14191F),
          shape: ContinuousRectangleBorder(
            borderRadius: BorderRadius.circular(24.0),
          ),
        ),
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Image Section
              Container(
                width: 120,
                constraints: const BoxConstraints(minHeight: 100),
                child: Image.network(
                  imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[700],
                      child: const Icon(
                        Icons.image_not_supported,
                        color: Colors.grey,
                        size: 40,
                      ),
                    );
                  },
                ),
              ),
              // Content Section
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      DefaultTextStyle(
                        style: const TextStyle(
                          color: Color(0xffEFF2F5),
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          height: 1.2,
                        ),
                        child: TextView(
                          text: title,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: DefaultTextStyle(
                          style: TextStyle(
                            color: Color(0xffA2B0C3),
                            fontSize: 14,
                            height: 1.3,
                          ),
                          child: TextView(
                            text: subtitle,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 24.verticalSpace,
// LiveStreamSection(),
// 32.verticalSpace,
// Padding(
//   padding: const EdgeInsets.symmetric(horizontal: 20.0),
//   child: Column(
//     children: [
//       CupertinoButton(
//         onPressed: () {},
//         padding: EdgeInsets.zero,
//         minimumSize: Size(0, 0),
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             TextView(
//               text: "Kurdistan",
//               style: TextStyle(
//                 color: Color(0xffEFF2F5).withValues(alpha: 0.7),
//                 fontSize: 20.sp,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//             Icon(
//               CupertinoIcons.arrow_up_right,
//               size: 24.r,
//               color: Color(0xff3693FF).withValues(alpha: 0.7),
//             ),
//           ],
//         ),
//       ),
//       12.verticalSpace,
//       Divider(color: const Color(0xff28323E), height: 1, thickness: 1),
//     ],
//   ),
// ),

 // sectionState.maybeWhen(
                      //   orElse: () => const SizedBox.shrink(),
                      //   loading: () => const LoadingView(),
                      //   error: (error, StackTrace stackTrace) => Padding(
                      //     padding: const EdgeInsets.all(16.0),
                      //     child: TextView(
                      //       text: error.toString(),
                      //       style: const TextStyle(color: Colors.red),
                      //     ),
                      //   ),
                      //   data: (sections) => Column(
                      //     children: [
                      //       CarouselSlider(
                      //         options: CarouselOptions(
                      //           height: 300.r,
                      //           viewportFraction: 0.92,
                      //           pageSnapping: true,
                      //           enableInfiniteScroll: false,
                      //           pauseAutoPlayOnTouch: true,
                      //           autoPlayCurve: Curves.fastOutSlowIn,
                      //           autoPlay: true,
                      //           autoPlayInterval: const Duration(seconds: 7),
                      //           onPageChanged: (index, reason) {
                      //             HapticFeedback.mediumImpact();
                      //             setState(() {
                      //               _currentIndex = index;
                      //             });
                      //           },
                      //           // enlargeStrategy: CenterPageEnlargeStrategy.zoom,
                      //           // enlargeCenterPage: true,
                      //         ),
                      //         items: sections.map(
                      //           (SectionEntity section) {
                      //             return CustomContextMenu(
                      //               previewBuilder: (context, animation, child) {
                      //                 return Column(
                      //                   mainAxisAlignment: MainAxisAlignment.center,
                      //                   mainAxisSize: MainAxisSize.max,
                      //                   children: [
                      //                     Expanded(
                      //                       child: CardViewPreview(
                      //                         imageUrl: "https://test-kurdsatnews-portal.rndlabs.dev${section.imageUrl}",
                      //                         dutation: 50,
                      //                         title: section.title,
                      //                       ),
                      //                     ),
                      //                   ],
                      //                 );
                      //               },

                      //               isSelected: true,
                      //               onPress: () {
                      //                 context.pop();
                      //               },
                      //               actions: [
                      //                 // harkate dasanem ba actiony har itemka shshalay rashabe,
                      //                 // tasawrakam hokar bottom menuaka be ale set state bangakay la backward aniumation kate
                      //                 // objectaka bwny nia la asla abe ama chara bkre.......
                      //                 // agar bawana chara nakra tanha wa bkam actions requrie nabe w bas agina hich keshayaky nya.
                      //                 Text(
                      //                   "data",
                      //                   style: TextStyle(
                      //                     color: Colors.red,
                      //                     fontSize: 16.sp,
                      //                   ),
                      //                 ),
                      //                 CustomContextMenuAction(
                      //                   trailingIcon: Icons.remove_circle_outline_rounded,
                      //                   isDestructiveAction: true,
                      //                   onPressed: () {
                      //                     Navigator.pop(context);
                      //                   },
                      //                   child: Text(
                      //                     "هەڵبژاردن",
                      //                     style: TextStyle(
                      //                       color: Colors.red,
                      //                       fontSize: 16.sp,
                      //                     ),
                      //                   ),
                      //                 ),
                      //                 CustomContextMenuAction(
                      //                   trailingIcon: Icons.share_outlined,

                      //                   onPressed: () {
                      //                     Navigator.pop(context);
                      //                   },
                      //                   child: Text(
                      //                     "هاوبەشکردن",
                      //                     style: TextStyle(
                      //                       color: Colors.red,
                      //                       fontSize: 16.sp,
                      //                     ),
                      //                   ),
                      //                 ),
                      //               ],

                      //               child: CardView(
                      //                 imageUrl: "https://test-kurdsatnews-portal.rndlabs.dev${section.imageUrl}",
                      //                 dutation: 10,
                      //                 title: section.title,
                      //               ),
                      //             );
                      //           },
                      //         ).toList(),
                      //       ),
                      //       16.verticalSpace,
                      //       Row(
                      //         mainAxisAlignment: MainAxisAlignment.center,
                      //         children: List.generate(
                      //           sections.length,
                      //           (index) => AnimatedContainer(
                      //             duration: const Duration(milliseconds: 300),
                      //             curve: Curves.fastOutSlowIn,

                      //             width: 8.r,
                      //             height: 8.r,
                      //             margin: EdgeInsets.symmetric(horizontal: 4.r),
                      //             decoration: BoxDecoration(
                      //               shape: BoxShape.circle,
                      //               color: index == _currentIndex ? Color(0xff5194E1) : Color(0xff28323E),
                      //             ),
                      //           ),
                      //         ),
                      //       ),
                      //     ],
                      //   ),
                      // ),

                      // carosel inducator
                      // 32.verticalSpace,
                      // sectionState.maybeWhen(
                      //   orElse: () => const SizedBox.shrink(),
                      //   loading: () => const LoadingView(),
                      //   error: (error, StackTrace stackTrace) => Padding(
                      //     padding: const EdgeInsets.all(16.0),
                      //     child: TextView(
                      //       text: error.toString(),
                      //       style: const TextStyle(color: Colors.red),
                      //     ),
                      //   ),
                      //   data: (sections) => CarouselSlider(
                      //     options: CarouselOptions(
                      //       aspectRatio: 16 / 13,
                      //       viewportFraction: 0.92,
                      //     ),
                      //     items: sections.map(
                      //       (SectionEntity section) {
                      //         return CardView(
                      //           imageUrl: "https://test-kurdsatnews-portal.rndlabs.dev${section.imageUrl}",
                      //           dutation: 10,
                      //           title: section.title,
                      //         );
                      //       },
                      //     ).toList(),
                      //   ),
                      // ),





  // Widget _buildTabContent(KurdsatNewsHomeDataEntity data) {
  //   if (data) {
  //     return Center(
  //       child: Padding(
  //         padding: EdgeInsets.all(40.0),
  //         child: Column(
  //           children: [
  //             Icon(
  //               Icons.article_outlined,
  //               size: 48.r,
  //               color: Color(0xffA2B0C3),
  //             ),
  //             16.verticalSpace,
  //             Text(
  //               "No news available",
  //               style: TextStyle(
  //                 color: Color(0xffA2B0C3),
  //                 fontSize: 16.sp,
  //               ),
  //             ),
  //           ],
  //         ),
  //       ),
  //     );
  //   }

  //   // get only tab title

  //   return Center(
  //     child: Padding(
  //       padding: EdgeInsets.all(40.0),
  //       child: Column(
  //         children: [
  //           Text(data.data?[selectedTabIndex].title ?? ""),
  //         ],
  //       ),
  //     ),
  //   );

  //   return ListView.separated(
  //     shrinkWrap: true,
  //     physics: NeverScrollableScrollPhysics(),
  //     itemCount: data.data?.length ?? 0,
  //     separatorBuilder: (context, index) => 16.verticalSpace,
  //     itemBuilder: (context, index) {
  //       final KurdsatNewsHomeDataEntity item = data.data?[index] ?? const KurdsatNewsHomeDataEntity(viewMode: '', articleCategories: [], articles: []);
  //       return Padding(
  //         padding: EdgeInsets.symmetric(horizontal: 20.0),
  //         child: InkWell(
  //           onTap: () {
  //             // Handle news item tap
  //             // Navigate to detail page
  //           },
  //           child: Container(
  //             padding: EdgeInsets.all(16.0),
  //             decoration: BoxDecoration(
  //               color: Color(0xff14191F),
  //               borderRadius: BorderRadius.circular(12),
  //               border: Border.all(
  //                 color: Color(0xff28323E),
  //                 width: 1,
  //               ),
  //             ),
  //             child: Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 // Category badge
  //                 if (item.title != null && item.title!.isNotEmpty) ...[
  //                   Container(
  //                     padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
  //                     decoration: BoxDecoration(
  //                       color: Color(0xff3693FF).withOpacity(0.2),
  //                       borderRadius: BorderRadius.circular(12),
  //                     ),
  //                     child: Text(
  //                       item.title!,
  //                       style: TextStyle(
  //                         color: Color(0xff3693FF),
  //                         fontSize: 10.sp,
  //                         fontWeight: FontWeight.w500,
  //                       ),
  //                     ),
  //                   ),
  //                   8.verticalSpace,
  //                 ],

  //                 // News title
  //                 Text(
  //                   item.title ?? "No title",
  //                   style: TextStyle(
  //                     color: Color(0xffEFF2F5),
  //                     fontSize: 16.sp,
  //                     fontWeight: FontWeight.bold,
  //                     height: 1.3,
  //                   ),
  //                   maxLines: 2,
  //                   overflow: TextOverflow.ellipsis,
  //                 ),

  //                 // if (item.description != null && item.description!.isNotEmpty) ...[
  //                 //   8.verticalSpace,
  //                 //   Text(
  //                 //     item.description!,
  //                 //     style: TextStyle(
  //                 //       color: Color(0xffA2B0C3),
  //                 //       fontSize: 14.sp,
  //                 //       height: 1.4,
  //                 //     ),
  //                 //     maxLines: 3,
  //                 //     overflow: TextOverflow.ellipsis,
  //                 //   ),
  //                 // ],
  //                 12.verticalSpace,

  //                 // Metadata row
  //                 // Row(
  //                 //   children: [
  //                 //     Icon(
  //                 //       Icons.access_time,
  //                 //       size: 14.r,
  //                 //       color: Color(0xff3693FF),
  //                 //     ),
  //                 //     4.horizontalSpace,
  //                 //     Text(
  //                 //       item.publishedAt ?? "Unknown time", // Adjust field name
  //                 //       style: TextStyle(
  //                 //         color: Color(0xff3693FF),
  //                 //         fontSize: 12.sp,
  //                 //       ),
  //                 //     ),
  //                 //     Spacer(),
  //                 //     Icon(
  //                 //       CupertinoIcons.arrow_up_right,
  //                 //       size: 16.r,
  //                 //       color: Color(0xffA2B0C3),
  //                 //     ),
  //                 //   ],
  //                 // ),
  //               ],
  //             ),
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }