import 'package:kurdsat_application/src/core/constants/const.dart';

const String kGoogleCheckEndpoint = "https://google.com";

class Api {
  String get baseUrl => "";

  //
  //
  //
  //   _  __             _           _     _   _
  //  | |/ /            | |         | |   | \ | |
  //  | ' /_   _ _ __ __| |___  __ _| |_  |  \| | _____      _____
  //  |  <| | | | '__/ _` / __|/ _` | __| | . ` |/ _ \ \ /\ / / __|
  //  | . \ |_| | | | (_| \__ \ (_| | |_  | |\  |  __/\ V  V /\__ \
  //  |_|\_\__,_|_|  \__,_|___/\__,_|\__| |_| \_|\___| \_/\_/ |___/

  String get _baseKurdsatNewsUrl => kBaseKurdsatNewsUrl;
  String get baseKurdsatNewsUrl => _baseKurdsatNewsUrl;

  String _constructKurdsatNewsUrl(String endpoint) => "$_baseKurdsatNewsUrl$endpoint";

  String get kurdsatNewsHomePage => _constructKurdsatNewsUrl("/mobile/newshomepage?pagination[limit]=4&locale=en");

  String kurdsatNewsArticle(int articleId) => _constructKurdsatNewsUrl("/articles/$articleId?populate[article_categories][fields][0]=title&populate[article_types][fields][0]=title&populate[image][populate][image][fields][0]=url&populate[author][fields][0]=full_name&populate[image][fields][0]=id&fields[0]=date&fields[1]=title&fields[2]=body&fields[3]=subtitle");

  String get kurdsatNewsChannels => _constructKurdsatNewsUrl("/channels?populate[channel_type][fields][0]=title&populate[image][fields][0]=url&locale=ckb&filters[channel_type][title][\$in][0]=TV&filters[channel_type][title][\$in][1]=Radio");

  //
  //
  //
  //   _  __             _ _     _
  //  | |/ /            | | |   (_)
  //  | ' /_   _ _ __ __| | |__  _ _ __
  //  |  <| | | | '__/ _` | '_ \| | '_ \
  //  | . \ |_| | | | (_| | |_) | | | | |
  //  |_|\_\__,_|_|  \__,_|_.__/|_|_| |_|

  String get _kurdBinBaseUrl => "https://test-kurdbin-portal.rndlabs.dev";
  String get kurdBinBaseUrl => _kurdBinBaseUrl;
  String _constructKurdBinUrl(String endpoint) => "$_kurdBinBaseUrl$endpoint";

  String get kurdBinHomePage => _constructKurdBinUrl("/api/mobile/kurdbinhomepage");

  String get kurdBinSeries => _constructKurdBinUrl("/api/series-groups");

  //
  //
  //
  //   _  __             _           _
  //  | |/ /            | |         | |
  //  | ' /_   _ _ __ __| |___  __ _| |_
  //  |  <| | | | '__/ _` / __|/ _` | __|
  //  | . \ |_| | | | (_| \__ \ (_| | |_
  //  |_|\_\__,_|_|  \__,_|___/\__,_|\__|

  String get _kurdsatBaseUrl => "https://test-kurdsat-portal.rndlabs.dev";
  String get kurdsatBaseUrl => _kurdsatBaseUrl;
  String _constructKurdsat(String endpoint) => "$_kurdsatBaseUrl$endpoint";

  String get kurdsatHomePage => _constructKurdsat("/api/mobile/kurdsathomepage");
}
