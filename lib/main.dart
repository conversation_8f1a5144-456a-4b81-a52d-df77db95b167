import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'src/app/widget/app.dart';
import 'src/core/utils/managers/http/bad_certificate_bypass.dart';
import 'src/injection.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  await init(); // Ensure GetIt is initialized before runApp

  EasyLocalization.logger.enableBuildModes = [];
  HttpOverrides.global = BadCertificateBypass();

  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  await dotenv.load(fileName: ".env");
  runApp(
    ProviderScope(
      child: EasyLocalization(
        supportedLocales: [Locale('en', 'US'), Locale('ar', 'AE'), Locale('ar', 'IQ')],
        path: "assets/translations",
        fallbackLocale: const Locale('en', 'US'),
        child: const MyApp(),
      ),
    ),
  );
}
