import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:kurdsat_application/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat_application/src/core/utils/helpers/error_parser.dart' show errorParser;
import 'package:kurdsat_application/src/features/{{feature_name.snakeCase()}}/data/datasources/{{feature_name.snakeCase()}}_remote_data_source.dart' show {{feature_name.pascalCase()}}RemoteDataSource;
import 'package:kurdsat_application/src/features/{{feature_name.snakeCase()}}/data/mappers/{{feature_name.snakeCase()}}_mapper.dart' show {{feature_name.pascalCase()}}Mapper;
import 'package:kurdsat_application/src/features/{{feature_name.snakeCase()}}/data/models/{{feature_name.snakeCase()}}_model_data.dart' show {{feature_name.pascalCase()}}ModelData;
import 'package:kurdsat_application/src/features/{{feature_name.snakeCase()}}/domain/entities/{{feature_name.snakeCase()}}_entity.dart' show {{feature_name.pascalCase()}}Entity;
import 'package:kurdsat_application/src/features/{{feature_name.snakeCase()}}/domain/repositories/{{feature_name.snakeCase()}}_repository.dart' show {{feature_name.pascalCase()}}Repository;

class {{feature_name.pascalCase()}}RepositoryImpl implements {{feature_name.pascalCase()}}Repository {
  final {{feature_name.pascalCase()}}RemoteDataSource {{feature_name.camelCase()}}RemoteDataSource;

  {{feature_name.pascalCase()}}RepositoryImpl({required this.{{feature_name.camelCase()}}RemoteDataSource});

  @override
  Future<Either<ErrorModel, List<{{feature_name.pascalCase()}}Entity>>> fetch{{feature_name.pascalCase()}}() async {
    try {
      final result = await {{feature_name.camelCase()}}RemoteDataSource.fetch{{feature_name.pascalCase()}}();

      {{feature_name.pascalCase()}}ModelData model = {{feature_name.pascalCase()}}ModelData.fromJson(result);

      {{feature_name.pascalCase()}}Mapper mapper = {{feature_name.pascalCase()}}Mapper({{feature_name.camelCase()}}ModelData: model);

      final List<{{feature_name.pascalCase()}}Entity> finalResponse = mapper.to{{feature_name.pascalCase()}}Entity();

      return Right(finalResponse);
    } catch (error, stackTrace) {
      log(error.toString(), stackTrace: stackTrace);
      return Left(errorParser(error, stackTrace));
    }
  }
}




