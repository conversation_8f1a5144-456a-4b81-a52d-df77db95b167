import 'package:dartz/dartz.dart' show Either;
import 'package:flutter_riverpod/flutter_riverpod.dart' show Provider;
import 'package:kurdsat_application/src/core/common/data/models/error_model/error_model.dart' show ErrorModel;
import 'package:kurdsat_application/src/features/{{feature_name.snakeCase()}}/data/datasources/{{feature_name.snakeCase()}}_remote_data_source.dart' show {{feature_name.camelCase()}}DatasourceProvider;
import 'package:kurdsat_application/src/features/{{feature_name.snakeCase()}}/data/repositories/{{feature_name.snakeCase()}}_reposetory_impl.dart' show {{feature_name.pascalCase()}}RepositoryImpl;
import 'package:kurdsat_application/src/features/{{feature_name.snakeCase()}}/domain/entities/{{feature_name.snakeCase()}}_entity.dart' show {{feature_name.pascalCase()}}Entity;

abstract class {{feature_name.pascalCase()}}Repository {
  Future<Either<ErrorModel, List<{{feature_name.pascalCase()}}Entity>>> fetch{{feature_name.pascalCase()}}();
}

final {{feature_name.camelCase()}}RepositoryProvider = Provider<{{feature_name.pascalCase()}}Repository>((ref) {
  return {{feature_name.pascalCase()}}RepositoryImpl({{feature_name.camelCase()}}RemoteDataSource: ref.read({{feature_name.camelCase()}}DatasourceProvider));
});
