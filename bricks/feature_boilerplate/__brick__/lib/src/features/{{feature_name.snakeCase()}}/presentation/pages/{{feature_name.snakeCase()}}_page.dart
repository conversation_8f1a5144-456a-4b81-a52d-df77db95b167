import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:kurdsat_application/src/features/{{feature_name.snakeCase()}}/presentation/logic/{{feature_name.snakeCase()}}_notifier.dart' show {{feature_name.camelCase()}}NotifierProvider;



class {{feature_name.pascalCase()}}Page extends ConsumerStatefulWidget {
  const {{feature_name.pascalCase()}}Page({super.key});

  @override
  ConsumerState<{{feature_name.pascalCase()}}Page> createState() => _{{feature_name.pascalCase()}}PageState();
}

class _{{feature_name.pascalCase()}}PageState extends ConsumerState<{{feature_name.pascalCase()}}Page> {
  @override
  Widget build(BuildContext context) {
    final {{feature_name.camelCase()}}State = ref.watch({{feature_name.camelCase()}}NotifierProvider);
    return Scaffold(
      backgroundColor: Color(0xff0A0C10),
      body: SingleChildScrollView(
        child: Column(
          children: [
            24.verticalSpace,
            // Continue

            {{feature_name.camelCase()}}State.maybeWhen(
              orElse: () => Text("Loading..."),
              data: (data) => Text("Data loaded: ${data.length} items"),
              loading: () => CircularProgressIndicator(),
              error: (error, stack) => Text("Error: $error"),
            ),
          ],
        ),
      ),
    );
  }
}
